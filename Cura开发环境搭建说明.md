# Cura并行开发环境搭建说明

本文档记录了在macOS系统上搭建Cura、CuraEngine和Uranium并行开发环境的完整过程。

## 项目概述

- **Cura**: 3D打印切片软件的主要用户界面和应用程序
- **Uranium**: Cura的底层框架，提供3D场景管理、插件系统等核心功能
- **CuraEngine**: 负责实际切片计算的C++引擎

## 系统要求

### macOS系统要求
- macOS 11 或更高版本
- Xcode 12 或更高版本
- apple-clang-12.0 或更高版本
- Python 3.12 或更高版本
- CMake 3.23 或更高版本
- Ninja 1.10 或更高版本
- Conan >=2.7.0 <3.0.0
- automake
- altool

### 推荐工具
- PyCharm (社区版免费) - 官方推荐的IDE
- Homebrew - 用于安装系统依赖

## 搭建步骤

### 步骤1: 系统依赖检查

首先检查系统是否满足要求：

```bash
# 检查Python版本
python3 --version

# 检查CMake版本
cmake --version

# 检查Ninja版本
ninja --version

# 检查Xcode命令行工具
xcode-select --version
```

### 步骤2: 创建Python虚拟环境

```bash
# 创建虚拟环境（如果还没有创建）
python3 -m venv cura_venv

# 激活虚拟环境
source cura_venv/bin/activate
```

### 步骤3: 安装和配置Conan

```bash
# 安装指定版本的Conan
pip install conan==2.7.0

# 配置Conan
conan config install https://github.com/ultimaker/conan-config.git
conan profile detect --force

# 移除需要认证的远程仓库（社区开发者需要）
conan remote remove cura
```

### 步骤4: 创建项目目录结构

建议将所有项目放在同一个根目录下，便于并行开发：

```
PythonProject/
├── cura_venv/          # Python虚拟环境
├── Cura/               # Cura主项目
├── Uranium/            # Uranium框架
├── CuraEngine/         # CuraEngine切片引擎
└── Cura开发环境搭建说明.md
```

### 步骤5: 克隆项目

在同一个根目录下克隆所有三个项目：

```bash
# 克隆Cura主项目
git clone https://github.com/Ultimaker/Cura.git

# 克隆Uranium框架
git clone https://github.com/Ultimaker/Uranium.git

# 克隆CuraEngine切片引擎
git clone https://github.com/Ultimaker/CuraEngine.git
```

### 步骤6: 配置Cura开发环境

进入Cura目录并初始化开发环境：

```bash
cd Cura

# 激活虚拟环境
source ../cura_venv/bin/activate

# 安装Cura依赖并创建开发环境
conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv
```

这个过程会：
- 下载并构建所有依赖项（首次运行需要30分钟以上）
- 创建Python虚拟运行环境
- 生成PyCharm运行配置

### 步骤7: 设置Uranium为可编辑模式

为了实现并行开发，需要将Uranium设置为可编辑模式：

```bash
# 在Cura目录下，激活虚拟环境
source venv/bin/activate

# 安装Uranium为可编辑模式
pip install -e ../Uranium
```

### 步骤8: 构建CuraEngine

CuraEngine是C++项目，需要单独构建：

```bash
cd ../CuraEngine

# 创建构建目录
mkdir build
cd build

# 配置CMake
cmake .. -DCMAKE_BUILD_TYPE=Release

# 构建
make -j$(nproc)
```

### 步骤9: 配置CuraEngine路径

需要告诉Cura在哪里找到CuraEngine：

```bash
# 回到Cura目录
cd ../../Cura

# 创建或编辑配置文件
# 在Cura的配置中指定CuraEngine的路径
```

## 当前状态

### 已完成的步骤
1. ✅ 创建Python虚拟环境 (`cura_venv`)
2. ✅ 安装和配置Conan 2.7.0
3. ✅ 克隆所有三个项目 (Cura, Uranium, CuraEngine)
4. ✅ 修复Conan编译器版本兼容性问题
5. ✅ 安装GitPython依赖
6. 🔄 正在构建CuraEngine依赖
7. 🔄 正在构建Cura依赖

### 项目目录结构
```
CuraProject/
├── cura_venv/              # Python虚拟环境 ✅
├── Uranium/                # Uranium框架 ✅
├── Cura/                   # Cura主项目 ✅
├── CuraEngine/             # CuraEngine切片引擎 ✅
└── Cura开发环境搭建说明.md
```

### 解决的问题
1. **Conan版本问题**: 从1.x升级到2.7.0
2. **编译器版本问题**: 将apple-clang从16降级到14以兼容依赖
3. **GitPython依赖**: 安装缺失的Git模块
4. **OpenSSL包损坏**: 清理损坏的缓存包并重新下载

### 当前进度
- ✅ 解决了OpenSSL包损坏问题
- 🔄 CuraEngine和Cura的依赖重新开始构建
- 这个过程可能需要30-60分钟，取决于网络速度和机器性能
- 首次构建会下载大量的C++库和Python包

### 构建监控
可以通过以下命令监控构建进度：

```bash
# 查看CuraEngine构建进度
cd CuraEngine
tail -f conan_install.log

# 查看Cura构建进度
cd Cura
tail -f conan_install.log
```

### 预期下载内容
- **CuraEngine**: 约20个C++依赖包（gRPC、Protobuf、Boost等）
- **Cura**: 约30个Python和C++依赖包（Qt、PyQt、Uranium等）
- **总下载量**: 约2-3GB的依赖包和构建工具

## 搭建进度

- [x] 创建Python虚拟环境
- [x] 创建搭建说明文档
- [x] 安装和配置Conan 2.7.0
- [x] 克隆Uranium项目
- [x] 克隆Cura项目
- [x] 克隆CuraEngine项目
- [x] 修复编译器兼容性问题
- [x] 安装GitPython依赖
- [ ] 构建CuraEngine依赖（进行中）
- [ ] 构建Cura依赖（进行中）
- [ ] 设置Uranium可编辑模式
- [ ] 配置并行开发环境
- [ ] 测试运行Cura
- [ ] 完善文档

## 注意事项

1. 确保所有项目都在同一个根目录下，这样便于配置并行开发
2. 使用虚拟环境可以避免与系统Python环境冲突
3. Conan配置只需要执行一次，除非清除缓存
4. 第一次运行conan install会下载大量依赖，可能需要30分钟以上

## 故障排除

### 常见问题及解决方案

1. **Conan编译器版本不兼容**
   ```bash
   # 修改默认配置文件
   sed -i '' 's/compiler.version=16/compiler.version=14/' ~/.conan2/profiles/default
   ```

2. **GitPython模块缺失**
   ```bash
   ./cura_venv/bin/python -m pip install GitPython
   ```

3. **网络下载缓慢**
   - 使用稳定的网络连接
   - 考虑使用VPN或镜像源
   - 耐心等待，首次构建需要下载大量依赖

4. **OpenSSL包损坏错误**
   ```bash
   # 清理损坏的OpenSSL包
   ./cura_venv/bin/conan remove "openssl*" --confirm

   # 重新运行构建
   cd CuraEngine && ../cura_venv/bin/conan install . --build=missing --update
   cd ../Cura && ../cura_venv/bin/conan install . --build=missing --update -g VirtualPythonEnv -g PyCharmRunEnv
   ```

5. **构建失败**
   - 检查系统依赖是否完整安装
   - 确保Xcode命令行工具已安装
   - 清除Conan缓存后重试：`conan remove "*" --confirm`

## 下一步计划

构建完成后将进行以下配置：

1. **设置Uranium可编辑模式**
   ```bash
   cd Cura
   source venv/bin/activate
   pip install -e ../Uranium
   ```

2. **配置CuraEngine路径**
   - 在Cura配置中指定CuraEngine可执行文件路径
   - 测试切片功能是否正常

3. **验证并行开发环境**
   - 修改Uranium代码测试热重载
   - 修改CuraEngine代码测试重新构建
   - 确保所有组件协同工作

4. **创建开发工作流**
   - 配置IDE（推荐PyCharm）
   - 设置调试环境
   - 建立代码同步和测试流程
