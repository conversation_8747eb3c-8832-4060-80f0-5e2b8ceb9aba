# PyCharm正确配置指南

## 🎯 问题分析

- ✅ PyQt6已经在Conan环境中正确安装
- ✅ 位置：`/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/cura_venv/lib/python3.12/site-packages`
- ❌ 问题：PyCharm的Python解释器找不到这个路径

## 🔧 解决方案：PYTHONPATH配置

### 1. Python解释器设置

**保持现有的解释器**：
```
/Users/<USER>/.conan2/p/cpyth346320b00af0c/p/bin/python3.12
```

### 2. 运行配置设置

在PyCharm运行配置中：

**脚本路径**：
```
/Users/<USER>/PycharmProjects/CuraProject/Cura/cura_app.py
```

**参数**：
```
--external-backend
```

**工作目录**：
```
/Users/<USER>/PycharmProjects/CuraProject/Cura
```

**🔑 关键：环境变量**：
```
PYTHONPATH=/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/cura_venv/lib/python3.12/site-packages:/Users/<USER>/PycharmProjects/CuraProject/Uranium:$PYTHONPATH
```

**运行前执行**：
```bash
source build/generators/conanrun.sh
```

### 3. 验证配置

在PyCharm的Python控制台中运行：

```python
# 设置路径（如果控制台中PYTHONPATH未生效）
import sys
sys.path.insert(0, '/Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/cura_venv/lib/python3.12/site-packages')
sys.path.insert(0, '/Users/<USER>/PycharmProjects/CuraProject/Uranium')

# 测试导入
from PyQt6.QtNetwork import QSslConfiguration
print("✅ PyQt6.QtNetwork导入成功")

from UM.Version import Version
v = Version('5.0.0')
print("✅ Uranium导入成功:", v.getDevModeInfo())
```

## 📋 配置步骤总结

1. **保持现有Python解释器**（Conan的Python 3.12）
2. **在运行配置中添加PYTHONPATH环境变量**
3. **指向Conan虚拟环境的site-packages目录**
4. **包含本地Uranium路径**

## 🎯 为什么这样配置

- **Python解释器**：使用稳定的Conan Python（无库链接问题）
- **PYTHONPATH**：告诉Python去哪里找PyQt6和其他包
- **路径优先级**：确保本地开发的Uranium优先于Conan预编译版本

## ✅ 成功标志

配置正确后：
- ✅ PyCharm中不再显示PyQt6导入错误
- ✅ 可以成功运行Cura应用程序
- ✅ 并行开发模式正常工作
- ✅ 本地Uranium修改立即生效

---

**核心要点**：通过PYTHONPATH环境变量让PyCharm的Python解释器能够找到Conan环境中的PyQt6包，而不是创建新的虚拟环境。
