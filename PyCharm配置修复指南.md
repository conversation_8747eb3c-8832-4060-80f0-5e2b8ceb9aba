# PyCharm配置修复指南

## 🚨 问题诊断

您遇到的错误是因为PyCharm使用了系统Python而不是Conan配置的Python环境。

**错误原因**:
- PyCharm当前使用: `/opt/homebrew/bin/python3` (系统Python 3.13)
- 应该使用: Conan配置的Python 3.12环境

## 🔧 解决方案

### 方法1: 使用Conan Python解释器（推荐）

1. **打开PyCharm设置**
   ```
   PyCharm → Preferences → Project → Python Interpreter
   ```

2. **添加新的解释器**
   - 点击齿轮图标 → Add...
   - 选择 "System Interpreter"
   - 解释器路径设置为:
   ```
   /Users/<USER>/.conan2/p/cpyth346320b00af0c/p/bin/python3.12
   ```

3. **验证解释器**
   - 确保显示 Python 3.12.x
   - 包列表中应该包含 PyQt6 6.6.0

### 方法2: 使用虚拟环境（备选）

1. **创建基于Conan的虚拟环境**
   ```bash
   cd /Users/<USER>/PycharmProjects/CuraProject/Cura
   /Users/<USER>/.conan2/p/cpyth346320b00af0c/p/bin/python3.12 -m venv venv_dev
   source venv_dev/bin/activate
   
   # 安装必要的包（如果需要）
   pip install -e ../Uranium
   ```

2. **在PyCharm中配置**
   - 解释器路径: `Cura/venv_dev/bin/python`

## 🔄 运行配置修复

### 1. 修改现有运行配置

**脚本路径**: `/Users/<USER>/PycharmProjects/CuraProject/Cura/cura_app.py`

**参数**: `--external-backend`

**工作目录**: `/Users/<USER>/PycharmProjects/CuraProject/Cura`

**环境变量**:
```
PYTHONPATH=../Uranium:$PYTHONPATH
```

**运行前执行**:
```bash
cd /Users/<USER>/PycharmProjects/CuraProject/Cura
source build/generators/conanrun.sh
```

### 2. 创建新的运行配置

1. **创建Shell脚本运行配置**
   - 名称: `Cura (开发模式)`
   - 脚本内容:
   ```bash
   #!/bin/bash
   cd /Users/<USER>/PycharmProjects/CuraProject
   source setup_dev_environment.sh
   cd Cura
   python3 cura_app.py --external-backend
   ```

## 🧪 验证配置

### 在PyCharm中测试

1. **打开Python控制台**
   ```python
   import sys
   print("Python版本:", sys.version)
   print("Python路径:", sys.executable)
   
   # 测试PyQt6
   from PyQt6.QtNetwork import QSslConfiguration
   print("✅ PyQt6导入成功")
   
   # 测试Uranium
   from UM.Version import Version
   v = Version('5.0.0')
   print(v.getDevModeInfo())
   ```

2. **预期输出**:
   ```
   Python版本: 3.12.x
   Python路径: /Users/<USER>/.conan2/p/cpyth346320b00af0c/p/bin/python3.12
   ✅ PyQt6导入成功
   ✅ Parallel development mode active! Version: 5.0.0 (Modified at runtime)
   ```

## 🛠️ 命令行验证

在修改PyCharm配置前，先在命令行验证环境：

```bash
# 1. 设置环境
cd /Users/<USER>/PycharmProjects/CuraProject
source setup_dev_environment.sh

# 2. 进入Cura目录
cd Cura

# 3. 测试Python环境
python3 -c "
import sys
print('Python:', sys.executable)
from PyQt6.QtNetwork import QSslConfiguration
print('✅ PyQt6可用')
from UM.Version import Version
print('✅ Uranium可用')
"

# 4. 尝试启动Cura
python3 cura_app.py --help
```

## 📋 快速修复步骤

1. **立即修复PyCharm解释器**:
   ```
   设置 → 项目 → Python解释器
   → 选择: /Users/<USER>/.conan2/p/cpyth346320b00af0c/p/bin/python3.12
   ```

2. **修改运行配置环境变量**:
   ```
   PYTHONPATH=../Uranium:$PYTHONPATH
   ```

3. **添加运行前脚本**:
   ```bash
   source /Users/<USER>/PycharmProjects/CuraProject/Cura/build/generators/conanrun.sh
   ```

## 🔍 故障排除

### 如果仍然有问题

1. **检查Conan环境**:
   ```bash
   cd /Users/<USER>/PycharmProjects/CuraProject/Cura
   source build/generators/conanrun.sh
   which python3
   python3 -c "import PyQt6; print('PyQt6版本:', PyQt6.QtCore.PYQT_VERSION_STR)"
   ```

2. **重新构建Cura**:
   ```bash
   cd /Users/<USER>/PycharmProjects/CuraProject/Cura
   ~/.local/bin/conan install . --build=missing
   ```

3. **清理PyCharm缓存**:
   ```
   File → Invalidate Caches and Restart
   ```

## ✅ 验证成功标志

配置正确后，您应该能够：
- ✅ 在PyCharm中导入PyQt6而不报错
- ✅ 在PyCharm中导入Uranium模块
- ✅ 成功运行Cura应用程序
- ✅ 看到并行开发模式的标识

---

**重要提示**: 确保每次重启PyCharm后，运行配置中的环境变量和路径设置仍然正确。
